"""Entry file for managing speech commands.

Contains all commands for speech.
"""

import typer

from .cli import SpeechCL<PERSON>
from .config import config

app = typer.Typer()


@app.command()
def code(hlc_path: str = None):
    if not config.model and not config.api_key:
        config.model, config.api_key = SpeechCLI.select_model()


@app.command()
def config(name: str, formal: bool = False):
    if formal:
        print(f"Goodbye Ms. {name}. Have a good day.")
    else:
        print(f"Bye {name}!")


if __name__ == "__main__":
    app()
