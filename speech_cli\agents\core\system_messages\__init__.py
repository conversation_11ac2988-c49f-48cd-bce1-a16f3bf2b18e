from pathlib import Path

import markdown
from bs4 import BeautifulSoup


class SystemMessages:
    """Retrieve the system message.

    Make sure the system message is stored in .txt file.
    And that the attribute name matches the file name.

    Example:
        >>> SystemMessages.promptor  # returns promptor.txt

    """

    current_dir = Path(__file__).parent

    def _load_file(self, file: Path):
        """Extract raw text from a markdown file, removing all formatting.

        Args:
            file: Path to the markdown file

        Returns:
            str: Plain text content without markdown formatting

        """
        with file.open(encoding="utf-8") as f:
            content = f.read()

        # Convert markdown to HTML
        html = markdown.markdown(content)

        # Extract text from HTML
        soup = BeautifulSoup(html, "html.parser")
        return soup.get_text(separator=" ", strip=True)

    def __getattr__(self, name: str):
        file = self.current_dir / f"{name.lower()}.md"

        if not file.exists():
            raise FileNotFoundError(
                "No file named {name}.md found in the system messages directory."
            )

        return self._load_file(file)
