from collections.abc import Callable
from typing import Any

from langgraph.graph import StateGraph


class BaseAgent:
    """Base agent class for generator and translator agents."""

    overall_state: Any | None = None
    input_schema: Any | None = None
    output_schema: Any | None = None
    tools: list[Callable] | None = None

    @property
    def get_overall_state(self):
        """Returns the overall state if available.

        Else return a state that inherits from input schema and output schema.
        """
        try:
            if self.overall_state:
                return self.overall_state
            elif self.input_schema and self.output_schema:
                return type("OverallState", (self.input_schema, self.output_schema), {})

            raise AttributeError(
                "Missing overall state attribute, make sure to specify the overall"
                " state or the input and output schema."
            )
        except Exception as err:
            raise err

    def add_edges(self, builder):
        raise NotImplementedError("'add_edges' method, is not implemented yet.")

    @property
    def nodes(self):
        raise NotImplementedError("Missing nodes property.")

    def add_nodes(self, builder):
        """Adds nodes to builder."""
        try:
            for node_name, node in self.nodes:
                builder.add_node(node_name, node)
        except Exception as err:
            raise err

        return self.add_edges(builder)

    def build(self):
        """Builds a graph representation of the agent."""
        builder = StateGraph(
            self.get_overall_state,
            input_schema=self.input_schema,
            output_schema=self.output_schema,
        )

        return self.add_nodes(builder).compile()

    def run(self, initial_state: Any) -> Any:
        """Entry point to every agent."""
        if not hasattr(self, "graph"):
            self.graph = self.build()
        return self.entry_point(initial_state)
