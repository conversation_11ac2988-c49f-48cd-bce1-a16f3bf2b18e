[build-system]
requires = ["setuptools>=61.0.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "speech-cli"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "beautifulsoup4>=4.13.4",
    "langchain-core>=0.3.72",
    "langchain-google-genai>=2.1.8",
    "langgraph>=0.6.2",
    "markdown>=3.8.2",
    "prompt-toolkit>=3.0.51",
    "pyperclip>=1.9.0",
    "python-dotenv>=1.1.1",
]

[project.scripts]
speech = "speech_cli.cli:setup"

[tool.setuptools.packages.find]
where = ["."]
include = ["speech_cli*"]

