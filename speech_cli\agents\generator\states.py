from operator import add
from typing import Annotated, Literal

from langchain_core.messages import AnyMessage
from langgraph.graph import add_messages
from pydantic import BaseModel, Field


class _Step(BaseModel):
    """The individual step within a plan."""

    step: str = Field(
        description="The next step to be executed on by the orchestrator node."
    )
    done: bool = Field(description="Whether the step has been completed or not.")


class OverallState(BaseModel):
    """The overall state.

    Contains all the necessary state keys.
    """

    messages: Annotated[list[AnyMessage], add_messages] = Field(
        description="The list of messages."
    )
    prompt: str = Field(
        description="The generated prompt or response for the planner or end"
        " node respectively, from the promptor node."
    )
    route: Literal["editor", "planner", "__end__"] = Field(
        description="The route to choose between the end, editor and planner nodes."
    )
    plan: list[_Step] | None = Field(
        description="The list of steps to be executed on by the orchestrator node."
    )
    task: str = Field(
        description="The next task to be executed on by the editor node or a final"
        " summary of the tasks, explaining what were being worked on."
    )
    mode: Literal["create", "edit", "append"] | None = Field(
        description="The mode, the editor will work in, between create, edit and"
        " append."
    )
    node: int | None = Field(
        description="The node to target for modification in modify mode. If None,"
        " the editor will create or rewrite the entire file, if it's already existing."
    )


class InputOutputSchema(BaseModel):
    """The input and output schema to the promptor node."""

    messages: Annotated[list[AnyMessage], add_messages] = Field(
        description="The list of messages."
    )


class PromptorStructuredOutput(BaseModel):
    """The promptor llm structured output state."""

    prompt: str = Field(
        description="The generated prompt or response for the orchestrator or end"
        " node respectively, from the promptor node."
    )
    route: Literal["planner", "__end__"] = Field(
        description="The route to choose between the end and planner nodes."
    )


class PlannerStructuredOutput(BaseModel):
    """The planner llm structured output state."""

    plan: list[str] = Field(
        description="The list of steps to be executed on by the orchestrator node."
    )


class OrchestratorStructuredOutput(BaseModel):
    """The orchestrator llm structured output state."""

    plan: list[_Step] | None = Field(
        description="The list of steps to be executed on by the orchestrator node."
    )
    task: str = Field(
        description="The next task to be executed on by the editor node or a final"
        " summary of the tasks, explaining what were being worked on."
    )
    mode: Literal["create", "edit", "append"] = Field(
        description="The mode, the editor will work in, between create, edit and"
        " append."
    )
    node: int | None = Field(
        description="The node to target for modification in modify mode. If None,"
        " the editor will create or rewrite the entire file, if it's already existing."
    )
    route: Literal["editor", "__end__"] = Field(
        description="The route to choose between the end and editor nodes."
    )


class EditorStructuredOutput(BaseModel):
    """The orchestrator llm structured output state."""

    prompt: str = Field(
        description="The generated prompt or response for the planner or end"
        " node respectively, from the promptor node."
    )
    route: Literal["editor", "planner", "__end__"] = Field(
        description="The route to choose between the end, editor and planner nodes."
    )
    plan: list[_Step] | None = Field(
        description="The list of steps to be executed on by the orchestrator node."
    )
    task: str = Field(
        description="The next task to be executed on by the editor node or a final"
        " summary of the tasks, explaining what were being worked on."
    )
    mode: Literal["create", "edit", "append"] | None = Field(
        description="The mode, the editor will work in, between create, edit and"
        " append."
    )
    node: int | None = Field(
        description="The node to target for modification in modify mode. If None,"
        " the editor will create or rewrite the entire file, if it's already existing."
    )
