import os
import subprocess
from pathlib import Path


class HLC:
    """A class representation of the HLC github organization.

    Enables the synchronization of important HLC github organisation repositories, for
    data retrieval and upload via git.
    """

    local_directory = Path.home() / ".hlc"
    github_org_url = "https://github.com/human-language-code"
    repos = {"hlc": github_org_url + "/HLC.git"}

    def sync_git_repository(self, repo, branch="main", depth=None) -> tuple[bool, str]:
        """Clone the HLC repository if it doesn't exist, or pull the latest commits.

        This function handles both scenarios:
        1. If the target directory doesn't exist or isn't a Git repository, it clones
        2. If the target directory exists and is a Git repository, it pulls latest
        commits

        Args:
            repo (str): The name of the Git repository (retrieved via HTTPS)
            branch (str): The name of the branch to retrieve.
            depth (Optional[int]): Create shallow clone with limited history. If None,
            full

        Returns:
            tuple[bool, str]: A tuple containing:
                - bool: True if operation was successful, False otherwise
                - str: Success message or error description

        Examples:
            >>> success, message = sync_git_repository("hlc")
            >>> if success:
            ...     print(f"Repository synced: {message}")
            ... else:
            ...     print(f"Error: {message}")

            >>> # Clone specific branch with shallow history
            >>> success, message = sync_git_repository(
            ...     "https://github.com/user/repo.git",
            ...     "/path/to/local/repo",
            ...     branch="develop",
            ...     depth=1,
            ... )

        """
        git_dir = self.local_directory / ".git"

        try:
            # Check if target directory exists and is a Git repository
            if self.local_directory.exists() and git_dir.exists():
                return self._pull_latest_commits(self.local_directory, branch)
            else:
                return self._clone_repository(repo, self.local_directory, branch, depth)

        except Exception as e:
            return False, f"Unexpected error: {str(e)}"

    @classmethod
    def _clone_repository(
        cls,
        repo_url: str,
        target_path: Path,
        branch: str | None = None,
        depth: int | None = None,
    ) -> tuple[bool, str]:
        """Clone a Git repository to the specified directory.

        Args:
            repo_url (str): The URL of the Git repository
            target_path (Path): The target directory path
            branch (Optional[str]): Specific branch to clone
            depth (Optional[int]): Depth for shallow clone

        Returns:
            tuple[bool, str]: Success status and message

        """
        try:
            # Create parent directories if they don't exist
            target_path.parent.mkdir(parents=True, exist_ok=True)

            # Build the clone command
            cmd = ["git", "clone"]

            # Add branch specification if provided
            if branch:
                cmd.extend(["--branch", branch])

            # Add depth specification for shallow clone if provided
            if depth:
                cmd.extend(["--depth", str(depth)])

            cmd.extend([repo_url, str(target_path)])

            # Execute the clone command
            subprocess.run(cmd, capture_output=True, text=True, check=True)

            return True, f"Repository successfully cloned to {target_path}"

        except subprocess.CalledProcessError as e:
            error_msg = e.stderr.strip() if e.stderr else str(e)
            return False, f"Failed to clone repository: {error_msg}"
        except Exception as e:
            return False, f"Error during clone operation: {str(e)}"

    @classmethod
    def clone_repository(cls):
        """Clones the default HLC repo to user's home directory."""
        git_dir = cls.local_directory / ".git"

        if not cls.local_directory.exists() or not git_dir.exists():
            return cls._clone_repository(cls.repo["hlc"], cls.local_directory)

    def _pull_latest_commits(
        self, repo_path: Path, branch: str | None = None
    ) -> tuple[bool, str]:
        """Pull the latest commits from the remote repository.

        Args:
            repo_path (Path): Path to the existing Git repository
            branch (Optional[str]): Specific branch to checkout and pull

        Returns:
            tuple[bool, str]: Success status and message

        """
        try:
            # Change to the repository directory
            original_cwd = Path.cwd()
            os.chdir(repo_path)

            try:
                # If a specific branch is requested, checkout that branch
                if branch:
                    # Fetch all branches first
                    subprocess.run(
                        ["git", "fetch", "--all"],
                        capture_output=True,
                        text=True,
                        check=True,
                    )

                    # Check if branch exists locally
                    result = subprocess.run(
                        ["git", "branch", "--list", branch],
                        capture_output=True,
                        text=True,
                    )

                    if result.stdout.strip():
                        # Branch exists locally, checkout and pull
                        subprocess.run(
                            ["git", "checkout", branch],
                            capture_output=True,
                            text=True,
                            check=True,
                        )
                    else:
                        # Branch doesn't exist locally, checkout from remote
                        subprocess.run(
                            ["git", "checkout", "-b", branch, f"origin/{branch}"],
                            capture_output=True,
                            text=True,
                            check=True,
                        )

                # Pull the latest commits
                result = subprocess.run(
                    ["git", "pull"], capture_output=True, text=True, check=True
                )

                # Check if there were any updates
                if "Already up to date" in result.stdout:
                    return True, f"Repository at {repo_path} is already up to date"
                else:
                    return (
                        True,
                        f"Repository at {repo_path} successfully updated with latest"
                        " commits",
                    )

            finally:
                # Always restore the original working directory
                os.chdir(original_cwd)

        except subprocess.CalledProcessError as e:
            error_msg = e.stderr.strip() if e.stderr else str(e)
            return False, f"Failed to pull latest commits: {error_msg}"
        except Exception as e:
            return False, f"Error during pull operation: {str(e)}"

    def get_repository_info(self, repo_path: str) -> tuple[bool, dict]:
        """Get information about a Git repository.

        Args:
            repo_path (str): Path to the Git repository

        Returns:
            tuple[bool, dict]: Success status and repository information dictionary

        Example:
            >>> success, info = get_repository_info("/path/to/repo")
            >>> if success:
            ...     print(f"Current branch: {info['current_branch']}")
            ...     print(f"Remote URL: {info['remote_url']}")
            ...     print(f"Last commit: {info['last_commit']}")

        """
        target_path = Path(repo_path).resolve()

        if not target_path.exists() or not (target_path / ".git").exists():
            return False, {"error": "Directory is not a Git repository"}

        try:
            original_cwd = Path.cwd()
            os.chdir(target_path)

            try:
                info = {}

                # Get current branch
                result = subprocess.run(
                    ["git", "branch", "--show-current"],
                    capture_output=True,
                    text=True,
                    check=True,
                )
                info["current_branch"] = result.stdout.strip()

                # Get remote URL
                result = subprocess.run(
                    ["git", "remote", "get-url", "origin"],
                    capture_output=True,
                    text=True,
                    check=True,
                )
                info["remote_url"] = result.stdout.strip()

                # Get last commit info
                result = subprocess.run(
                    ["git", "log", "-1", "--pretty=format:%H|%an|%ad|%s"],
                    capture_output=True,
                    text=True,
                    check=True,
                )
                if result.stdout:
                    commit_parts = result.stdout.split("|", 3)
                    info["last_commit"] = {
                        "hash": commit_parts[0],
                        "author": commit_parts[1],
                        "date": commit_parts[2],
                        "message": commit_parts[3] if len(commit_parts) > 3 else "",
                    }

                # Check if there are uncommitted changes
                result = subprocess.run(
                    ["git", "status", "--porcelain"],
                    capture_output=True,
                    text=True,
                    check=True,
                )
                info["has_uncommitted_changes"] = bool(result.stdout.strip())

                return True, info

            finally:
                os.chdir(original_cwd)

        except subprocess.CalledProcessError as e:
            return False, {
                "error": f"Git command failed: {e.stderr.strip() if e.stderr else str(e)}"
            }
        except Exception as e:
            return False, {"error": f"Unexpected error: {str(e)}"}
