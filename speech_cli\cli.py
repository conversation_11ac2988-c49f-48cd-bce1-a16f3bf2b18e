import sys
from logging.config import dictConfig
from pathlib import Path

from dotenv import dotenv_values
from prompt_toolkit import HTML, PromptSession, print_formatted_text, prompt
from prompt_toolkit.application import Application
from prompt_toolkit.enums import EditingMode
from prompt_toolkit.key_binding import KeyBindings
from prompt_toolkit.layout import Layout
from prompt_toolkit.layout.containers import HSplit, Window
from prompt_toolkit.layout.controls import FormattedTextControl
from prompt_toolkit.styles import Style

from .agents import Generator, Translator
from .config import LOGGING_CONFIG, config

# AIzaSyDud0kFOMqmBYMFED_wDu250hX8iW0Zpis


class SpeechCLI:
    """Speech CLI tool."""

    models = [
        "gemini-2.5-pro",
        "gemini-2.5-flash",
        "gemini-1.5-flash",
        "gemini-2.5-flash-lite",
    ]
    style = Style.from_dict(
        {
            "prompt": "#006ba6 bold",
            "input": "#0077be",
            "output": "#0099cc",
            "border": "#62b6cb",
            "timestamp": "#bee9e8",
            "title": "#005577 bold underline",
            "accent": "#1b4965",
            "success": "#cae9ff",
            "error": "#ff6b6b",
            "menu": "#5fa8d3",
            "dialog": "bg:#4444aa",
            "dialog frame.label": "bg:#ffffff #000000",
            "dialog.body": "bg:#000000 #00aa00",
            "dialog shadow": "bg:#00aa44",
            "selected": "bg:#662266 #ffffff bold",
            "unselected": "#bbbbbb",
            "pointer": "#00aa00 bold",
            "radiolist focused": "bg:#662266 #ffffff",
            "button focused": "bg:#662266 #ffffff",
        }
    )

    def __init__(self):
        self.generator = Generator()
        self.translator = Translator()
        self.session = PromptSession()

    def output(self, text):
        print_formatted_text(HTML(f"<output>{text}</output>"), style=self.style)

    def input(self, text):
        return self.session.prompt(HTML(f"<input>{text}</input>"), style=self.style)

    @classmethod
    def select_model(cls):
        """Select AI model."""
        selected_index = [0]  # wrap in list for mutability in closures

        kb = KeyBindings()

        @kb.add("up")
        def _up(event):
            if selected_index[0] > 0:
                selected_index[0] -= 1
            event.app.layout.focus(container)

        @kb.add("down")
        def _down(event):
            if selected_index[0] < len(cls.models) - 1:
                selected_index[0] += 1
            event.app.layout.focus(container)

        @kb.add("enter")
        def _enter(event):
            event.app.exit(result=cls.models[selected_index[0]])

        def get_menu_text():
            result = []
            for i, m in enumerate(cls.models):
                if i == selected_index[0]:
                    result.append(("reverse", f"> {m}\n"))
                else:
                    result.append(("", f"  {m}\n"))
            return result

        container = Window(FormattedTextControl(get_menu_text), height=len(cls.models))

        root_container = HSplit(
            [
                Window(
                    height=1,
                    content=FormattedTextControl(
                        lambda: "Select a model (Use ↑/↓ and Enter):"
                    ),
                ),
                container,
            ]
        )

        style = Style.from_dict(
            {
                "": "#006ba6 bold",
            }
        )

        app = Application(
            layout=Layout(root_container),
            key_bindings=kb,
            style=style,
            full_screen=False,
        )

        model = app.run()

        # Create keybindings for API key input with paste support
        api_kb = KeyBindings()

        @api_kb.add("c-v")
        def _paste(event):
            """Paste from clipboard."""
            try:
                import pyperclip

                clipboard_content = pyperclip.paste()
                event.current_buffer.insert_text(clipboard_content)
            except ImportError:
                # Fallback if pyperclip is not available
                pass

        api_key = prompt(
            HTML("<input>Enter your api key: </input>"),
            style=cls.style,
            key_bindings=api_kb,
            editing_mode=EditingMode.EMACS,
        )

        def create_dot_speech():
            cwd = Path.cwd()
            speech_dir = cwd / ".speech"

            env_file = speech_dir / ".env"
            gitignore_file = cwd / ".gitignore"

            # Create the directory if it doesn't exist
            speech_dir.mkdir(parents=True, exist_ok=True)

            # Write or overwrite the .env file
            try:
                with env_file.open("w", encoding="utf-8") as f:
                    f.write("# Speech CLI environment variables.\n")
                    f.write(
                        "# This file is autogenerated, don not directly "
                        "edit it directly.\n"
                    )
                    f.write(f"MODEL={model}\n")
                    f.write(f"API_KEY={api_key}\n")
            except OSError as e:
                raise OSError(f"Failed to write .env file at {env_file}: {e}") from e
            else:
                # Add .speech/.env to .gitignore
                mode = "a" if gitignore_file.exists() else "w"

                with gitignore_file.open(mode, encoding="utf-8") as f:
                    f.write("\n# Ignore the .env in .speech directory\n")
                    f.write(".speech/.env")

        create_dot_speech()

        return model, api_key

    def run(self):
        """Entry point for the CLI tool."""
        try:
            while True:
                prompt = self.input("Your question: ")

                if prompt == "quit":
                    break

                response = self.generator.run(prompt)
                # response = self.translator.run(hlc_path)
                # print("here is your response: ", response)
                self.output(response["response"])

        except Exception as e:
            print_formatted_text(f"❌ An error occurred: {e}")
            sys.exit(1)
        finally:
            self.output("👋 Goodbye!")


def setup():
    """Sets up speech CLI."""
    if not config.model and not config.api_key:
        config.model, config.api_key = SpeechCLI.select_model()

    dictConfig(LOGGING_CONFIG)
    cli = SpeechCLI()
    cli.run()


if __name__ == "__main__":
    setup()
