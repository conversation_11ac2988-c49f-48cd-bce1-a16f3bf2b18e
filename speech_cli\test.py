#!/usr/bin/env python3
"""Beautiful Text CLI Tool
A colorful text input and display tool using prompt_toolkit
"""

import datetime
import random
import sys

from prompt_toolkit import print_formatted_text, prompt
from prompt_toolkit.application import Application
from prompt_toolkit.completion import Word<PERSON>ompleter
from prompt_toolkit.formatted_text import FormattedText
from prompt_toolkit.history import InMemoryHistory
from prompt_toolkit.key_binding import KeyBindings
from prompt_toolkit.layout.containers import HSplit, VSplit, Window
from prompt_toolkit.layout.controls import FormattedTextControl
from prompt_toolkit.layout.layout import Layout
from prompt_toolkit.shortcuts import clear, confirm
from prompt_toolkit.styles import Style
from prompt_toolkit.widgets import Box, Frame, TextArea


class BeautifulTextCLI:
    def __init__(self):
        self.history = InMemoryHistory()
        self.text_entries = []
        self.current_theme = "rainbow"

        # Define multiple color themes
        self.themes = {
            "rainbow": Style.from_dict(
                {
                    "prompt": "#ff6b6b bold",
                    "input": "#4ecdc4",
                    "output": "#45b7d1",
                    "border": "#96ceb4",
                    "timestamp": "#feca57",
                    "title": "#ff9ff3 bold underline",
                    "accent": "#54a0ff",
                    "success": "#5f27cd",
                    "error": "#ff3838",
                    "menu": "#00d2d3",
                }
            ),
            "ocean": Style.from_dict(
                {
                    "prompt": "#006ba6 bold",
                    "input": "#0077be",
                    "output": "#0099cc",
                    "border": "#62b6cb",
                    "timestamp": "#bee9e8",
                    "title": "#005577 bold underline",
                    "accent": "#1b4965",
                    "success": "#cae9ff",
                    "error": "#ff6b6b",
                    "menu": "#5fa8d3",
                }
            ),
            "sunset": Style.from_dict(
                {
                    "prompt": "#d63031 bold",
                    "input": "#e17055",
                    "output": "#fdcb6e",
                    "border": "#fd79a8",
                    "timestamp": "#6c5ce7",
                    "title": "#a29bfe bold underline",
                    "accent": "#fd79a8",
                    "success": "#00b894",
                    "error": "#d63031",
                    "menu": "#e84393",
                }
            ),
            "forest": Style.from_dict(
                {
                    "prompt": "#00b894 bold",
                    "input": "#00cec9",
                    "output": "#55a3ff",
                    "border": "#a29bfe",
                    "timestamp": "#fdcb6e",
                    "title": "#00b894 bold underline",
                    "accent": "#6c5ce7",
                    "success": "#00b894",
                    "error": "#e17055",
                    "menu": "#74b9ff",
                }
            ),
        }

        # Command completions
        self.commands = [
            "help",
            "clear",
            "history",
            "theme",
            "save",
            "load",
            "quit",
            "exit",
        ]
        self.completer = WordCompleter(self.commands, ignore_case=True)

    def get_current_style(self):
        """Get the current theme style."""
        return self.themes[self.current_theme]

    def print_banner(self):
        """Print a beautiful banner."""
        banner = [
            ("class:title", "✨ Beautiful Text CLI Tool ✨\n"),
            ("class:accent", "━" * 50 + "\n"),
            ("class:menu", "Commands: "),
            ("class:input", "help"),
            ("class:menu", " | "),
            ("class:input", "theme <name>"),
            ("class:menu", " | "),
            ("class:input", "clear"),
            ("class:menu", " | "),
            ("class:input", "quit\n"),
            ("class:accent", "━" * 50 + "\n"),
        ]
        print_formatted_text(FormattedText(banner), style=self.get_current_style())

    def display_text_beautifully(self, text, entry_number=None):
        """Display text with beautiful formatting."""
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")

        # Create a decorative border
        border_char = "─"
        corner_chars = ["╭", "╮", "╰", "╯"]

        # Calculate width based on text length
        max_width = max(len(text), 40)
        border_line = border_char * (max_width + 2)

        # Create formatted display
        display = [
            ("class:border", f"{corner_chars[0]}{border_line}{corner_chars[1]}\n"),
            ("class:border", "│ "),
            ("class:timestamp", f"[{timestamp}]"),
            ("class:border", " " * (max_width - len(timestamp) - 1) + "│\n"),
            ("class:border", "│ "),
            ("class:output", text),
            ("class:border", " " * (max_width - len(text) + 1) + "│\n"),
            ("class:border", f"{corner_chars[2]}{border_line}{corner_chars[3]}\n"),
        ]

        if entry_number is not None:
            print_formatted_text(
                FormattedText([("class:accent", f"\n📝 Entry #{entry_number}:\n")]),
                style=self.get_current_style(),
            )

        print_formatted_text(FormattedText(display), style=self.get_current_style())

    def show_help(self):
        """Display help information."""
        help_text = [
            ("class:title", "\n🎨 Beautiful Text CLI - Help\n"),
            ("class:accent", "━" * 40 + "\n"),
            ("class:menu", "Available Commands:\n\n"),
            ("class:input", "help"),
            ("class:menu", "          - Show this help message\n"),
            ("class:input", "clear"),
            ("class:menu", "         - Clear the screen\n"),
            ("class:input", "history"),
            ("class:menu", "       - Show text entry history\n"),
            ("class:input", "theme <name>"),
            ("class:menu", "  - Change color theme\n"),
            ("class:input", "save"),
            ("class:menu", "          - Save current session\n"),
            ("class:input", "quit/exit"),
            ("class:menu", "     - Exit the application\n\n"),
            ("class:accent", "Available Themes: "),
            ("class:success", ", ".join(self.themes.keys()) + "\n"),
            ("class:accent", "━" * 40 + "\n"),
        ]
        print_formatted_text(FormattedText(help_text), style=self.get_current_style())

    def show_history(self):
        """Display all text entries."""
        if not self.text_entries:
            print_formatted_text(
                FormattedText([("class:error", "📭 No text entries yet!\n")]),
                style=self.get_current_style(),
            )
            return

        print_formatted_text(
            FormattedText(
                [
                    (
                        "class:title",
                        f"\n📚 Text History ({len(self.text_entries)} entries):\n",
                    )
                ]
            ),
            style=self.get_current_style(),
        )

        for i, (text, timestamp) in enumerate(self.text_entries, 1):
            entry_display = [
                ("class:accent", f"{i:2d}. "),
                ("class:timestamp", f"[{timestamp}] "),
                ("class:output", text + "\n"),
            ]
            print_formatted_text(
                FormattedText(entry_display), style=self.get_current_style()
            )

    def change_theme(self, theme_name):
        """Change the color theme."""
        if theme_name in self.themes:
            self.current_theme = theme_name
            print_formatted_text(
                FormattedText(
                    [("class:success", f"🎨 Theme changed to '{theme_name}'!\n")]
                ),
                style=self.get_current_style(),
            )
        else:
            available = ", ".join(self.themes.keys())
            print_formatted_text(
                FormattedText(
                    [
                        (
                            "class:error",
                            f"❌ Unknown theme '{theme_name}'. Available: {available}\n",
                        )
                    ]
                ),
                style=self.get_current_style(),
            )

    def get_rainbow_text(self, text):
        """Create rainbow colored text."""
        colors = [
            "#ff6b6b",
            "#4ecdc4",
            "#45b7d1",
            "#96ceb4",
            "#feca57",
            "#ff9ff3",
            "#54a0ff",
        ]
        rainbow_text = []
        for i, char in enumerate(text):
            color = colors[i % len(colors)]
            rainbow_text.append((f"fg:{color}", char))
        return rainbow_text

    def create_fancy_prompt(self):
        """Create a fancy colored prompt."""
        prompt_chars = ["❯", "➤", "⚡", "🌟", "✨"]
        char = random.choice(prompt_chars)

        return FormattedText(
            [
                ("class:accent", "\n┌─ "),
                ("class:prompt", "Beautiful Text CLI"),
                ("class:accent", " ─\n│\n└─ "),
                ("class:prompt", f"{char} "),
            ]
        )

    def save_session(self):
        """Save current session to a file."""
        if not self.text_entries:
            print_formatted_text(
                FormattedText([("class:error", "📭 Nothing to save!\n")]),
                style=self.get_current_style(),
            )
            return

        filename = (
            f"text_session_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        )
        try:
            with open(filename, "w", encoding="utf-8") as f:
                f.write("Beautiful Text CLI Session\n")
                f.write("=" * 30 + "\n\n")
                for i, (text, timestamp) in enumerate(self.text_entries, 1):
                    f.write(f"{i:2d}. [{timestamp}] {text}\n")

            print_formatted_text(
                FormattedText(
                    [("class:success", f"💾 Session saved to '{filename}'!\n")]
                ),
                style=self.get_current_style(),
            )
        except Exception as e:
            print_formatted_text(
                FormattedText([("class:error", f"❌ Error saving file: {e}\n")]),
                style=self.get_current_style(),
            )

    def run(self):
        """Main application loop."""
        clear()
        self.print_banner()

        print_formatted_text(
            FormattedText(
                [
                    (
                        "class:success",
                        "💬 Start typing your text! Use 'help' for commands.\n",
                    )
                ]
            ),
            style=self.get_current_style(),
        )

        entry_count = 0

        while True:
            try:
                # Create fancy prompt
                fancy_prompt = self.create_fancy_prompt()

                # Get user input with autocompletion and history
                user_input = prompt(
                    fancy_prompt,
                    style=self.get_current_style(),
                    completer=self.completer,
                    history=self.history,
                    complete_style="column",
                    mouse_support=True,
                ).strip()

                if not user_input:
                    continue

                # Handle commands
                if user_input.lower() in ["quit", "exit"]:
                    if confirm(
                        "\n🚪 Are you sure you want to quit?",
                        style=self.get_current_style(),
                    ):
                        print_formatted_text(
                            FormattedText(
                                [
                                    (
                                        "class:success",
                                        "👋 Goodbye! Thanks for using Beautiful Text CLI!\n",
                                    )
                                ]
                            ),
                            style=self.get_current_style(),
                        )
                        break
                    continue

                elif user_input.lower() == "help":
                    self.show_help()
                    continue

                elif user_input.lower() == "clear":
                    clear()
                    self.print_banner()
                    continue

                elif user_input.lower() == "history":
                    self.show_history()
                    continue

                elif user_input.lower().startswith("theme "):
                    theme_name = user_input[6:].strip()
                    self.change_theme(theme_name)
                    continue

                elif user_input.lower() == "save":
                    self.save_session()
                    continue

                # Regular text input - display it beautifully
                entry_count += 1
                timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                self.text_entries.append((user_input, timestamp))

                # Display the text with beautiful formatting
                self.display_text_beautifully(user_input, entry_count)

                # Add some sparkle for fun
                sparkles = ["✨", "🌟", "⭐", "💫", "🔮"]
                sparkle = random.choice(sparkles)
                print_formatted_text(
                    FormattedText(
                        [
                            (
                                "class:accent",
                                f"{sparkle} Text captured beautifully! {sparkle}\n",
                            )
                        ]
                    ),
                    style=self.get_current_style(),
                )

            except KeyboardInterrupt:
                if confirm(
                    "\n\n🚪 Do you want to exit?", style=self.get_current_style()
                ):
                    print_formatted_text(
                        FormattedText([("class:success", "\n👋 Goodbye!\n")]),
                        style=self.get_current_style(),
                    )
                    break
                print_formatted_text(
                    FormattedText(
                        [
                            (
                                "class:menu",
                                "\n💡 Use 'quit' to exit or continue typing...\n",
                            )
                        ]
                    ),
                    style=self.get_current_style(),
                )
            except EOFError:
                print_formatted_text(
                    FormattedText([("class:success", "\n👋 Goodbye!\n")]),
                    style=self.get_current_style(),
                )
                break


def main():
    """Entry point for the CLI tool."""
    try:
        cli = BeautifulTextCLI()
        cli.run()
    except Exception as e:
        print(f"❌ An error occurred: {e}")
        sys.exit(1)




if __name__ == "__main__":
    main()
