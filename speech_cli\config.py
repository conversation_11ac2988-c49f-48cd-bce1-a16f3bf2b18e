import json
from pathlib import Path
from typing import Any

from dotenv import dotenv_values

LOGGING_CONFIG = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "verbose": {
            "format": """{levelname} - Line no.
            {lineno:d} in "{funcName}" function in "{filename}" - {pathname}:
            " {message}" at Time: {asctime} """,
            "style": "{",
        },
        "simple": {
            "format": "{levelname} - '{filename}': '{message}', {pathname}",
            "style": "{",
        },
    },
    "filters": {
        "require_debug_true": {
            "()": "speech_cli.filters.RequireDebugTrue",
        },
        "require_debug_false": {
            "()": "speech_cli.filters.RequireDebugFalse",
        },
        # "allow_only_debug_logs": {
        #     "()": "apps.core.utils.log_filters.AllowOnlyDebugLogs",
        # },
    },
    "handlers": {
        "dev_console": {
            "level": "DEBUG",
            "filters": ["require_debug_true"],
            "class": "logging.StreamHandler",
            "formatter": "verbose",
        },
        "dev_log_file": {
            "level": "WARNING",
            "class": "logging.FileHandler",
            "filters": ["require_debug_true"],
            "formatter": "verbose",
            # Creates a file with name speech.log in the base directory
            "filename": ".speech/speech.log",
        },
        "prod_log_file": {
            "level": "WARNING",
            "class": "logging.FileHandler",
            "filters": ["require_debug_false"],
            "formatter": "simple",
            # Creates a file with name speech.log in the base directory
            "filename": ".speech/speech.log",
        },
    },
    "loggers": {
        "": {
            "handlers": [
                "dev_console",
                "dev_log_file",
                "prod_log_file",
            ],
            "level": "DEBUG",
        },
    },
}


class Config:
    """Configuration management class for Speech CLI.

    This class provides a unified interface for managing configuration settings
    by loading from both config.json and .env files. It supports both local
    project-specific configuration and global user configuration.

    Configuration Loading Priority:
    1. Local project configuration (.speech/ in current working directory)
    2. Global user configuration (~/.speech/ in user home directory)
    3. Default configuration values

    File Types Supported:
    - config.json: JSON format for structured configuration data
    - .env: Environment variables format for sensitive data like API keys

    Local vs Global Configuration:
    - Local: Located in .speech/ directory within your project
    - Global: Located in ~/.speech/ directory in user home
    - Local configuration takes precedence over global configuration

    Usage Examples:
    ```python
    from speech_cli.config import config

    # Access configuration variables
    model = config.model
    api_key = config.api_key
    verbose = config.verbose

    # Set configuration values (saves automatically)
    config.set("model", "gemini-2.5-pro")
    config.set("api_key", "your-api-key-here")

    # Update multiple values at once
    config.update(model="gemini-2.5-flash", verbose=True)

    # Get with default value
    timeout = config.get("timeout", 30)
    ```

    Configuration Structure:
    The config.json file follows this structure:
    ```json
    {
      "model": "gemini-2.5-pro",
      "api_key": null,
      "settings": {
        "output_format": "text",
        "verbose": false,
        "timeout": 30
      }
    }
    ```

    Environment Variables (.env):
    ```
    MODEL=gemini-2.5-pro
    API_KEY=your-secret-api-key
    VERBOSE=true
    TIMEOUT=60
    ```

    Attributes:
        model (str): AI model to use for generation
        api_key (str): API key for authentication
        output_format (str): Output format preference
        verbose (bool): Enable verbose logging
        timeout (int): Request timeout in seconds

    """

    _default_config: dict[str, Any] = {
        "model": None,
        "api_key": None,
        "debug": False,
    }
    _config_file_name = "config.json"
    _env_file_name = ".env"

    def __init__(self):
        """Initialize configuration manager.

        The initialization process:
        1. Sets up configuration directory path
        2. Loads existing config.json if available
        3. Loads .env file if available
        4. Creates default configuration if none exists

        """
        self._project_speech_dir = Path.cwd() / ".speech"
        self._user_speech_dir = Path.home() / ".speech"
        self._config_data: dict[str, Any] = (
            self._default_config | self._load_config() | self._load_env()
        )

    def _load_env(self):
        """Load configuration from .env file."""
        # Locate the .env files
        project_env_file = self._project_speech_dir / ".env"
        user_env_file = self._user_speech_dir / ".env"

        # Load environment variables without polluting os.environ
        if project_env_file.exists():
            config = dotenv_values(project_env_file)

        if project_env_file.exists():
            config |= dotenv_values(user_env_file)

        new_config = {}
        for key, value in config.items():
            new_config[key.lower()] = value

        return new_config

    def _read_config(self, config_file: Path):
        """Returns json file if it exists."""
        if config_file.exists():
            try:
                with config_file.open("r", encoding="utf-8") as f:
                    return json.load(f)
            except (json.JSONDecodeError, OSError):
                return {}

        return {}

    def _load_config(self) -> None:
        """Load configuration from config.json file."""
        project_config_file = self._project_speech_dir / self._config_file_name
        user_config_file = self._user_speech_dir / self._config_file_name

        return self._read_config(project_config_file) | self._read_config(
            user_config_file
        )

    def save(self, key: str, value: Any, user=False) -> None:
        """Save current configuration to config.json file.

        Creates the .speech directory if it doesn't exist and writes
        the current configuration data to config.json in a formatted,
        human-readable JSON structure.

        Note: This method is automatically called by set() and update()
        methods, so manual calling is usually not necessary.
        """
        setattr(self, key, value)

        config_dir = self._project_speech_dir if not user else self._user_speech_dir
        # Ensure the .speech directory exists
        config_dir.mkdir(parents=True, exist_ok=True)
        config_file: Path = config.dir / self._config_file_name

        try:
            with config_file.open("r", encoding="utf-8") as f:
                config_data = json.load(f) | {key: value}
            with config_file.open("w", encoding="utf-8") as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
        except OSError:
            # Silently fail if unable to save config
            pass

    def __getattr__(self, name: str) -> Any:
        """Allow attribute-style access to config variables."""
        if name.startswith("_"):
            # Prevent access to private attributes
            raise AttributeError(
                f"'{self.__class__.__name__}' object has no attribute '{name}'"
            )

        if name in self._config_data:
            return self._config_data[name]

        raise AttributeError(f"Configuration '{name}' not found")

    def __setattr__(self, name: str, value: Any) -> None:
        """Allow attribute-style setting of config variables."""
        if name.startswith("_"):
            # Allow setting private attributes normally
            super().__setattr__(name, value)
            return

        # Set in main config data
        if hasattr(self, "_config_data"):
            self._config_data[name] = value
        else:
            # During initialization, set normally
            super().__setattr__(name, value)

    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value with optional default."""
        try:
            return getattr(self, key)
        except AttributeError:
            return default

    def to_dict(self) -> dict[str, Any]:
        """Return configuration as dictionary."""
        return self._config_data.copy()

    def __repr__(self) -> str:
        """String representation of config."""
        return f"Config({self._config_data})"


# Global config instance
config = Config()
