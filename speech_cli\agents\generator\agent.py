import logging

from langchain_core.messages import HumanMessage, SystemMessage
from langchain_google_genai import ChatGoogleGenerativeAI
from langgraph.graph import END, START
from langgraph.prebuilt import ToolNode, tools_condition

from speech_cli.agents import core
from speech_cli.agents.core import SystemMessages
from speech_cli.agents.core.utils import create_prompt, get_hlc_document
from speech_cli.config import config

from .conditional_nodes import editor_tools_condition, router_node
from .states import (
    InputOutputSchema,
    OrchestratorStructuredOutput,
    OverallState,
    PlannerStructuredOutput,
    PromptorStructuredOutput,
)

logger = logging.getLogger(__name__)


class Generator(core.BaseAgent):
    """Generator class for generating hlc code from prompt."""

    editor_tools = []

    overall_state = OverallState
    input_schema = InputOutputSchema
    output_schema = InputOutputSchema

    def get_llm(self, temperature=0.2):
        return ChatGoogleGenerativeAI(
            model=config.model,
            api_key=config.api_key,
            temperature=temperature,
        )

    def promptor_node(self, state: InputOutputSchema) -> OverallState:
        """Creates a good prompt with essential context for the planner node."""
        llm = self.get_llm()
        structured_llm = llm.with_structured_output(PromptorStructuredOutput)
        response: PromptorStructuredOutput = structured_llm.invoke(
            [SystemMessage(content=SystemMessages.promptor)] + state.messages
        )

        return response.model_dump()

    def planner_node(self, state: OverallState) -> OverallState:
        """Creates a plan for the orchestrator node."""
        llm = self.get_llm()
        structured_llm = llm.with_structured_output(PlannerStructuredOutput)
        prompt = create_prompt(SystemMessages.planner, state.prompt, get_hlc_document())
        response = structured_llm.invoke(prompt)

        return {"plan": [{"step": step, "done": False} for step in response.plan]}

    def orchestrator_node(self, state: OverallState) -> OverallState:
        """Guides the editor node to do a good job with enough context."""
        llm = self.get_llm(temperature=0.1)
        structured_llm = llm.with_structured_output(OrchestratorStructuredOutput)

        prompt = create_prompt(
            SystemMessages.orchestrator,
            get_hlc_document(),
            "The last task performed: ",
            state.task,
            plan=state.plan,
        )

        response = structured_llm.invoke(prompt)

        return (
            {"messages": response.task}
            if response.router == "__end__"
            else response.model_dump()
        )

    def editor_node(self, state: OverallState) -> OverallState:
        """Guides the editor node to do a good job with enough context."""
        llm = self.get_llm(temperature=0.1)
        llm_with_tools = llm.bind_tools(self.editor_tools, parallel_tool_calls=False)
        llm_with_tools.invoke([sys_msg] + state["messages"])

    def add_edges(self, builder):
        """Adds edges to builder."""
        builder.add_edge(START, "promptor")
        builder.add_conditional_edges("promptor", router_node)
        builder.add_edge("planner", "orchestrator")
        builder.add_conditional_edges("orchestrator", router_node)
        builder.add_conditional_edges("editor", editor_tools_condition)
        builder.add_edge("editor_tools", "editor")

        return builder

    def generate(self, prompt: str):
        return self.graph.invoke({"messages": prompt})

    @property
    def entry_point(self):
        return self.generate

    @property
    def nodes(self):
        return [
            ("editor", self.editor_node),
            ("orchestrator", self.orchestrator_node),
            ("planner", self.planner_node),
            ("promptor", self.promptor_node),
            ("editor_tools", ToolNode(self.editor_tools, name="editor_tools")),
        ]
